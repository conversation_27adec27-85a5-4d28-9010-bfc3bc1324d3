"use client"

import { useState, useMemo, useEffect } from "react"
import {
  Search,
  Filter,
  Building2,
  MapPin,
  Globe,
  Phone,
  Mail,
  Sparkles,
  GitCompare,
  X,
  Bookmark,
  BookmarkCheck,
  Map,
  Grid3X3,
  Loader2,
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ThemeToggle } from "@/components/theme-toggle"

type Organization = {
  id: number
  name: string
  category: string
  location: string
  employees: string
  website: string
  phone: string
  email: string
  description: string
  specialties: string[]
  founded: number
}

const categories = [
  "All Categories",
  "Research & Development",
  "Pharmaceuticals",
  "Medical Devices",
  "Diagnostics",
  "Investment & Funding",
  "Agricultural Biotechnology",
  "Marine Biotechnology",
  "Nanobiotechnology",
  "Bioinformatics & Data",
  "Synthetic Biology",
  "Regulatory & Compliance",
]

const employeeSizes = ["All Sizes", "10-50", "50-100", "100-500", "500-1000", "1000+"]

const isCompanyNameSearch = (query: string, organizations: Organization[]): boolean => {
  const companyNames = organizations.map((org) => org.name.toLowerCase())
  const queryLower = query.toLowerCase()

  return companyNames.some((name) => {
    // Check if query matches the full name or any word in the name
    if (name.includes(queryLower)) return true

    // Check if query matches any individual word in the company name
    const nameWords = name.split(/\s+/)
    return nameWords.some((word) => word.includes(queryLower) || queryLower.includes(word))
  })
}

const performIntelligentSearch = (query: string, organizations: Organization[]) => {
  if (!query.trim()) return organizations

  // Use regular search for company names
  if (isCompanyNameSearch(query, organizations)) {
    console.log("[v0] Using regular search for company name:", query)
    return organizations.filter((org) => org.name.toLowerCase().includes(query.toLowerCase()))
  }

  // Use AI-like search for complex queries
  console.log("[v0] Using intelligent search for complex query:", query)
  const queryLower = query.toLowerCase()

  // Enhanced semantic search with scoring
  const scoredResults = organizations.map((org) => {
    let score = 0

    // Direct matches get highest score
    if (org.name.toLowerCase().includes(queryLower)) score += 10
    if (org.description.toLowerCase().includes(queryLower)) score += 8
    if (org.category.toLowerCase().includes(queryLower)) score += 6

    // Specialty matches
    org.specialties.forEach((specialty) => {
      if (specialty.toLowerCase().includes(queryLower)) score += 5
    })

    // Location matches
    if (org.location.toLowerCase().includes(queryLower)) score += 4

    // Semantic keyword matching
    const keywords = {
      gene: ["gene therapy", "genetic", "genomic"],
      drug: ["pharmaceuticals", "drug discovery", "medicine"],
      ai: ["bioinformatics", "data", "analysis"],
      nano: ["nanobiotechnology", "nanoparticle"],
      marine: ["marine biotechnology", "ocean"],
      agriculture: ["agricultural biotechnology", "crop"],
      investment: ["venture capital", "funding"],
      regulatory: ["fda", "compliance", "approval"],
    }

    Object.entries(keywords).forEach(([key, synonyms]) => {
      if (queryLower.includes(key)) {
        synonyms.forEach((synonym) => {
          if (
            org.description.toLowerCase().includes(synonym) ||
            org.specialties.some((s) => s.toLowerCase().includes(synonym))
          ) {
            score += 3
          }
        })
      }
    })

    return { ...org, searchScore: score }
  })

  return scoredResults.filter((org) => org.searchScore > 0).sort((a, b) => b.searchScore - a.searchScore)
}

const parseCSV = (csvText: string): Organization[] => {
  console.log("[v0] Starting CSV parsing...")
  const lines = csvText.trim().split("\n")
  console.log("[v0] CSV lines count:", lines.length)

  if (lines.length < 2) {
    console.log("[v0] CSV has insufficient data - only", lines.length, "lines")
    return []
  }

  const headers = lines[0].split(",").map((h) => h.trim().replace(/"/g, ""))
  console.log("[v0] CSV headers:", headers)

  const organizations = lines
    .slice(1)
    .map((line, index) => {
      const values = line.split(",").map((v) => v.trim().replace(/"/g, ""))
      const org: any = {}

      headers.forEach((header, i) => {
        org[header] = values[i] || ""
      })

      const transformedOrg = {
        id: index + 1,
        name: org["Research area/lab/scientist"] || org.name || org.company_name || "",
        category: org["Organization"] || org.category || org.sector || "Research & Development",
        location: org["Location"] || org.location || org.city || "",
        employees: org.employees || org.size || "50-100",
        website: org["Weblink"] || org.website || org.url || "",
        phone: org["Contact Person"] || org.phone || org.telephone || "",
        email: org["Email"] || org.email || org.contact_email || "",
        description: org.description || org.about || "Biotechnology organization",
        specialties: org.specialties ? org.specialties.split(";").map((s: string) => s.trim()) : ["Biotechnology"],
        founded: Number.parseInt(org.founded || org.year_founded || "2020"),
      }

      if (index < 3) {
        console.log(`[v0] Transformed organization ${index + 1}:`, transformedOrg)
      }

      return transformedOrg
    })
    .filter((org) => org.name && org.name.trim() !== "") // Filter out entries with empty names

  console.log("[v0] Final organizations count after filtering:", organizations.length)
  return organizations
}

const useBookmarks = () => {
  const [bookmarks, setBookmarks] = useState<number[]>([])

  useEffect(() => {
    const saved = localStorage.getItem("biotech-bookmarks")
    if (saved) {
      setBookmarks(JSON.parse(saved))
    }
  }, [])

  const toggleBookmark = (orgId: number) => {
    const newBookmarks = bookmarks.includes(orgId) ? bookmarks.filter((id) => id !== orgId) : [...bookmarks, orgId]

    setBookmarks(newBookmarks)
    localStorage.setItem("biotech-bookmarks", JSON.stringify(newBookmarks))
  }

  const isBookmarked = (orgId: number) => bookmarks.includes(orgId)

  return { bookmarks, toggleBookmark, isBookmarked }
}

export default function BiotechDirectory() {
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [selectedEmployeeSize, setSelectedEmployeeSize] = useState("All Sizes")
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null)
  const [isAiSearch, setIsAiSearch] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [compareList, setCompareList] = useState<number[]>([])
  const [showComparison, setShowComparison] = useState(false)
  const [showBookmarksOnly, setShowBookmarksOnly] = useState(false)
  const [viewMode, setViewMode] = useState<"grid" | "map">("grid")
  const { bookmarks, toggleBookmark, isBookmarked } = useBookmarks()

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        setLoading(true)
        setError(null)

        console.log("[v0] Fetching data from GitHub Gist...")
        const response = await fetch(
          "https://gist.githubusercontent.com/RushiChaganti/54e669b7eee9f6bca2637458d0308f01/raw/",
        )

        if (!response.ok) {
          throw new Error(`Failed to fetch data: ${response.status}`)
        }

        const csvText = await response.text()
        console.log("[v0] Raw CSV data received:", csvText.substring(0, 500) + "...")
        console.log("[v0] CSV data length:", csvText.length)

        const parsedOrganizations = parseCSV(csvText)
        console.log("[v0] Parsed organizations count:", parsedOrganizations.length)
        console.log("[v0] First parsed organization:", parsedOrganizations[0])

        setOrganizations(parsedOrganizations)
      } catch (err) {
        console.error("[v0] Error fetching organizations:", err)
        setError(err instanceof Error ? err.message : "Failed to load organizations")
      } finally {
        setLoading(false)
      }
    }

    fetchOrganizations()
  }, [])

  const filteredOrganizations = useMemo(() => {
    let results = organizations

    if (showBookmarksOnly) {
      results = results.filter((org) => bookmarks.includes(org.id))
    }

    if (searchTerm.trim()) {
      const isCompanySearch = isCompanyNameSearch(searchTerm, organizations)
      setIsAiSearch(!isCompanySearch)
      results = performIntelligentSearch(searchTerm, results)
    }

    if (selectedCategory !== "All Categories") {
      results = results.filter((org) => org.category === selectedCategory)
    }

    if (selectedEmployeeSize !== "All Sizes") {
      results = results.filter((org) => org.employees === selectedEmployeeSize)
    }

    return results
  }, [searchTerm, selectedCategory, selectedEmployeeSize, showBookmarksOnly, bookmarks, organizations])

  const toggleCompare = (orgId: number) => {
    if (compareList.includes(orgId)) {
      setCompareList(compareList.filter((id) => id !== orgId))
    } else if (compareList.length < 5) {
      setCompareList([...compareList, orgId])
    }
  }

  const clearComparison = () => {
    setCompareList([])
    setShowComparison(false)
  }

  const compareOrganizations = organizations.filter((org) => compareList.includes(org.id))

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">Loading Organizations</h2>
          <p className="text-muted-foreground">Fetching biotechnology companies data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Building2 className="h-8 w-8 text-destructive mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">Error Loading Data</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <button
                onClick={() => {
                  setSelectedOrg(null)
                  setShowComparison(false)
                  setSearchTerm("")
                  setSelectedCategory("All Categories")
                  setSelectedEmployeeSize("All Sizes")
                  setShowBookmarksOnly(false)
                }}
                className="flex items-center gap-3 hover:opacity-80 transition-opacity"
              >
                <Building2 className="h-8 w-8 text-primary" />
                <h1 className="text-3xl font-bold text-foreground">Biogrofe</h1>
              </button>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowBookmarksOnly(!showBookmarksOnly)}
                className={`flex items-center gap-2 ${showBookmarksOnly ? "bg-primary text-primary-foreground" : ""}`}
              >
                <Bookmark className="h-4 w-4" />
                <span className="hidden sm:inline">Bookmarks ({bookmarks.length})</span>
              </Button>

              {compareList.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowComparison(!showComparison)}
                  className="flex items-center gap-2 bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800"
                >
                  <GitCompare className="h-4 w-4" />
                  <span className="hidden sm:inline">Compare ({compareList.length})</span>
                </Button>
              )}

              <div className="flex items-center border border-border rounded-md">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none border-r"
                >
                  <Grid3X3 className="h-4 w-4" />
                  <span className="hidden sm:inline ml-1">Grid</span>
                </Button>
                <Button
                  variant={viewMode === "map" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("map")}
                  className="rounded-l-none"
                >
                  <Map className="h-4 w-4" />
                  <span className="hidden sm:inline ml-1">Map</span>
                </Button>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                <span className="hidden sm:inline">Filters</span>
              </Button>
              <ThemeToggle />
            </div>
          </div>

          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              {isAiSearch && (
                <Sparkles className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-primary animate-pulse" />
              )}
              <Input
                placeholder="Search organizations, specialties, or ask complex questions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 pr-12 h-12 text-lg"
              />
            </div>
            {searchTerm && (
              <p className="text-sm text-muted-foreground mt-2 text-center">
                {isAiSearch ? (
                  <span className="flex items-center justify-center gap-1">
                    <Sparkles className="h-4 w-4 text-primary" />
                    Using AI-powered search
                  </span>
                ) : (
                  "Using direct company search"
                )}
              </p>
            )}
          </div>

          <p className="text-muted-foreground text-lg text-center mt-4">
            Discover leading biotechnology companies, research institutions, and industry partners
          </p>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {showFilters && (
            <>
              <div className="fixed inset-0 bg-black/50 z-40 lg:hidden" onClick={() => setShowFilters(false)} />
              <aside className="fixed top-0 right-0 h-full w-80 bg-background border-l border-border z-50 lg:relative lg:w-64 lg:h-auto lg:bg-transparent lg:border-l-0 lg:z-auto">
                <div className="p-4 lg:p-0">
                  <Card className="lg:sticky lg:top-4">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between text-base">
                        <span className="flex items-center gap-2">
                          <Filter className="h-4 w-4" />
                          Filters
                        </span>
                        <Button variant="ghost" size="sm" onClick={() => setShowFilters(false)}>
                          ×
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3 pt-0">
                      <div>
                        <label className="text-xs font-medium text-foreground mb-1 block">Category</label>
                        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-xs font-medium text-foreground mb-1 block">Company Size</label>
                        <Select value={selectedEmployeeSize} onValueChange={setSelectedEmployeeSize}>
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {employeeSizes.map((size) => (
                              <SelectItem key={size} value={size}>
                                {size}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="pt-2 border-t border-border">
                        <p className="text-xs text-muted-foreground">
                          {filteredOrganizations.length} organization{filteredOrganizations.length !== 1 ? "s" : ""}{" "}
                          found
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </aside>
            </>
          )}

          <main className="flex-1">
            {showComparison && compareOrganizations.length > 0 ? (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-semibold text-foreground">
                    Compare Organizations ({compareOrganizations.length})
                  </h2>
                  <Button variant="outline" onClick={() => setShowComparison(false)}>
                    Back to Results
                  </Button>
                </div>

                <div className="overflow-x-auto">
                  <div className="flex gap-4 min-w-max">
                    {compareOrganizations.map((org) => (
                      <Card key={org.id} className="w-80 flex-shrink-0">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="min-w-0 flex-1">
                              <CardTitle className="text-lg">{org.name}</CardTitle>
                              <CardDescription>{org.category}</CardDescription>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleCompare(org.id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <p className="text-sm text-muted-foreground">{org.description}</p>

                          <div className="space-y-3">
                            <div className="flex items-center gap-2 text-sm">
                              <MapPin className="h-4 w-4 text-primary flex-shrink-0" />
                              <span>{org.location}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Globe className="h-4 w-4 text-primary flex-shrink-0" />
                              <a
                                href={`https://${org.website}`}
                                className="text-primary hover:underline truncate"
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                {org.website}
                              </a>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Phone className="h-4 w-4 text-primary flex-shrink-0" />
                              <a href={`tel:${org.phone}`} className="text-primary hover:underline">
                                {org.phone}
                              </a>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Mail className="h-4 w-4 text-primary flex-shrink-0" />
                              <a href={`mailto:${org.email}`} className="text-primary hover:underline truncate">
                                {org.email}
                              </a>
                            </div>
                          </div>

                          <div>
                            <h4 className="font-medium text-sm mb-2">Specialties</h4>
                            <div className="flex flex-wrap gap-1">
                              {org.specialties.map((specialty) => (
                                <Badge key={specialty} variant="secondary" className="text-xs">
                                  {specialty}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                <div className="flex justify-center">
                  <Button variant="outline" onClick={clearComparison}>
                    Clear Comparison
                  </Button>
                </div>
              </div>
            ) : selectedOrg ? (
              <Card>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-2xl">{selectedOrg.name}</CardTitle>
                      <CardDescription className="text-lg mt-1">{selectedOrg.category}</CardDescription>
                    </div>
                    <Button variant="outline" onClick={() => setSelectedOrg(null)}>
                      Back to Results
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <p className="text-foreground text-lg leading-relaxed">{selectedOrg.description}</p>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <MapPin className="h-5 w-5 text-primary" />
                        <span>{selectedOrg.location}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Globe className="h-5 w-5 text-primary" />
                        <a href={`https://${selectedOrg.website}`} className="text-primary hover:underline">
                          {selectedOrg.website}
                        </a>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Phone className="h-5 w-5 text-primary" />
                        <span>{selectedOrg.phone}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Mail className="h-5 w-5 text-primary" />
                        <a href={`mailto:${selectedOrg.email}`} className="text-primary hover:underline truncate">
                          {selectedOrg.email}
                        </a>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-foreground mb-3">Specialties</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedOrg.specialties.map((specialty) => (
                        <Badge key={specialty} variant="secondary">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-semibold text-foreground">
                    {showBookmarksOnly ? "Bookmarked " : ""}Organizations ({filteredOrganizations.length})
                  </h2>
                  {compareList.length > 0 && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {compareList.length}/5 selected for comparison
                      </span>
                      <Button variant="outline" size="sm" onClick={clearComparison}>
                        Clear
                      </Button>
                    </div>
                  )}
                </div>

                {viewMode === "map" ? (
                  <div className="space-y-6">
                    <div className="relative bg-gradient-to-br from-slate-50 via-blue-50 to-green-50 dark:from-slate-950 dark:via-blue-950 dark:to-green-950 border border-border rounded-2xl p-8 overflow-hidden">
                      {/* Background Pattern */}
                      <div className="absolute inset-0 opacity-10">
                        <div
                          className="absolute inset-0"
                          style={{
                            backgroundImage: `radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px),
                                           radial-gradient(circle at 75% 75%, #10b981 2px, transparent 2px)`,
                            backgroundSize: "50px 50px",
                          }}
                        />
                      </div>

                      {/* Floating Particles */}
                      <div className="absolute inset-0 overflow-hidden pointer-events-none">
                        {[...Array(20)].map((_, i) => (
                          <div
                            key={i}
                            className="absolute w-2 h-2 bg-primary/20 rounded-full animate-pulse"
                            style={{
                              left: `${Math.random() * 100}%`,
                              top: `${Math.random() * 100}%`,
                              animationDelay: `${Math.random() * 3}s`,
                              animationDuration: `${2 + Math.random() * 2}s`,
                            }}
                          />
                        ))}
                      </div>

                      <div className="relative z-10">
                        <div className="text-center mb-8">
                          <div className="relative inline-block">
                            <Map className="h-16 w-16 text-primary mx-auto mb-4 drop-shadow-lg" />
                            <div className="absolute -inset-2 bg-primary/20 rounded-full blur-xl animate-pulse" />
                          </div>
                          <h3 className="text-3xl font-bold text-foreground mb-3 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                            Biotechnology Ecosystem Map
                          </h3>
                          <p className="text-muted-foreground text-lg">
                            Explore industry sectors as interconnected regions
                          </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                          {categories
                            .filter((category) => category !== "All Categories")
                            .map((category, index) => {
                              const categoryOrgs = filteredOrganizations.filter((org) => org.category === category)
                              if (categoryOrgs.length === 0) return null

                              // Enhanced color schemes with gradients
                              const stateStyles = [
                                "bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 border-blue-300 dark:border-blue-700 shadow-blue-200/50 dark:shadow-blue-900/50",
                                "bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-900 dark:to-emerald-800 border-emerald-300 dark:border-emerald-700 shadow-emerald-200/50 dark:shadow-emerald-900/50",
                                "bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900 dark:to-purple-800 border-purple-300 dark:border-purple-700 shadow-purple-200/50 dark:shadow-purple-900/50",
                                "bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900 dark:to-orange-800 border-orange-300 dark:border-orange-700 shadow-orange-200/50 dark:shadow-orange-900/50",
                                "bg-gradient-to-br from-rose-100 to-rose-200 dark:from-rose-900 dark:to-rose-800 border-rose-300 dark:border-rose-700 shadow-rose-200/50 dark:shadow-rose-900/50",
                                "bg-gradient-to-br from-teal-100 to-teal-200 dark:from-teal-900 dark:to-teal-800 border-teal-300 dark:border-teal-700 shadow-teal-200/50 dark:shadow-teal-900/50",
                                "bg-gradient-to-br from-indigo-100 to-indigo-200 dark:from-indigo-900 dark:to-indigo-800 border-indigo-300 dark:border-indigo-700 shadow-indigo-200/50 dark:shadow-indigo-900/50",
                                "bg-gradient-to-br from-pink-100 to-pink-200 dark:from-pink-900 dark:to-pink-800 border-pink-300 dark:border-pink-700 shadow-pink-200/50 dark:shadow-pink-900/50",
                                "bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900 dark:to-amber-800 border-amber-300 dark:border-amber-700 shadow-amber-200/50 dark:shadow-amber-900/50",
                                "bg-gradient-to-br from-cyan-100 to-cyan-200 dark:from-cyan-900 dark:to-cyan-800 border-cyan-300 dark:border-cyan-700 shadow-cyan-200/50 dark:shadow-cyan-900/50",
                                "bg-gradient-to-br from-lime-100 to-lime-200 dark:from-lime-900 dark:to-lime-800 border-lime-300 dark:border-lime-700 shadow-lime-200/50 dark:shadow-lime-900/50",
                              ]

                              const stateStyle = stateStyles[index % stateStyles.length]

                              return (
                                <div
                                  key={category}
                                  className={`group relative border-2 rounded-3xl p-6 transition-all duration-500 cursor-pointer transform hover:scale-110 hover:rotate-1 hover:shadow-2xl ${stateStyle}`}
                                  style={{
                                    clipPath:
                                      index % 4 === 0
                                        ? "polygon(0% 15%, 15% 0%, 85% 0%, 100% 15%, 100% 85%, 85% 100%, 15% 100%, 0% 85%)"
                                        : index % 4 === 1
                                          ? "polygon(0% 0%, 100% 0%, 85% 50%, 100% 100%, 0% 100%, 15% 50%)"
                                          : index % 4 === 2
                                            ? "polygon(20% 0%, 80% 0%, 100% 30%, 100% 70%, 80% 100%, 20% 100%, 0% 70%, 0% 30%)"
                                            : "polygon(0% 20%, 20% 0%, 80% 0%, 100% 20%, 90% 50%, 100% 80%, 80% 100%, 20% 100%, 0% 80%, 10% 50%)",
                                  }}
                                >
                                  {/* Glow Effect */}
                                  <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-3xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                                  {/* Animated Border */}
                                  <div className="absolute inset-0 rounded-3xl border-2 border-dashed border-primary/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse" />

                                  {/* State Header */}
                                  <div className="text-center mb-6 relative z-10">
                                    <h4 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors duration-300">
                                      {category.replace(" & ", "\n& ")}
                                    </h4>
                                    <div className="inline-flex items-center gap-2 bg-background/90 backdrop-blur-md rounded-full px-4 py-2 border shadow-lg">
                                      <MapPin className="h-4 w-4 text-primary animate-bounce" />
                                      <span className="text-sm font-semibold">{categoryOrgs.length} Companies</span>
                                    </div>
                                  </div>

                                  {/* State Content */}
                                  <div className="space-y-3 relative z-10">
                                    {categoryOrgs.slice(0, 3).map((org, orgIndex) => (
                                      <div
                                        key={org.id}
                                        className="bg-background/80 backdrop-blur-md border border-border/60 rounded-xl p-4 hover:bg-background/90 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
                                        style={{
                                          animationDelay: `${orgIndex * 0.1}s`,
                                        }}
                                      >
                                        <div className="flex items-start justify-between mb-3">
                                          <div className="flex items-center gap-2 min-w-0 flex-1">
                                            <Building2 className="h-4 w-4 text-primary flex-shrink-0 animate-pulse" />
                                            <span className="font-semibold text-sm truncate">{org.name}</span>
                                          </div>
                                          <div className="flex items-center gap-2">
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={(e) => {
                                                e.stopPropagation()
                                                toggleBookmark(org.id)
                                              }}
                                              className="p-1 h-auto hover:scale-110 transition-transform"
                                            >
                                              {isBookmarked(org.id) ? (
                                                <BookmarkCheck className="h-4 w-4 text-primary" />
                                              ) : (
                                                <Bookmark className="h-4 w-4 text-muted-foreground hover:text-primary" />
                                              )}
                                            </Button>
                                            <Checkbox
                                              checked={compareList.includes(org.id)}
                                              onCheckedChange={() => toggleCompare(org.id)}
                                              disabled={!compareList.includes(org.id) && compareList.length >= 5}
                                              className="h-4 w-4 hover:scale-110 transition-transform border-muted-foreground hover:border-primary data-[state=checked]:border-primary data-[state=checked]:bg-primary"
                                            />
                                          </div>
                                        </div>

                                        <div className="space-y-2">
                                          <p className="text-xs text-muted-foreground truncate flex items-center gap-1">
                                            <MapPin className="h-3 w-3 text-primary" />
                                            {org.location}
                                          </p>
                                          <div className="flex items-center gap-1 text-xs">
                                            <Globe className="h-3 w-3 text-primary flex-shrink-0" />
                                            <a
                                              href={`https://${org.website}`}
                                              className="text-primary hover:underline truncate hover:text-blue-600 transition-colors"
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              onClick={(e) => e.stopPropagation()}
                                            >
                                              {org.website}
                                            </a>
                                          </div>
                                          <div className="flex flex-wrap gap-1">
                                            {org.specialties.slice(0, 1).map((specialty) => (
                                              <Badge
                                                key={specialty}
                                                variant="secondary"
                                                className="text-xs bg-primary/10 text-primary border-primary/20"
                                              >
                                                {specialty}
                                              </Badge>
                                            ))}
                                          </div>
                                        </div>

                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="w-full mt-3 bg-transparent text-xs h-7 hover:bg-primary hover:text-primary-foreground transition-all duration-300 hover:shadow-md"
                                          onClick={(e) => {
                                            e.stopPropagation()
                                            setSelectedOrg(org)
                                          }}
                                        >
                                          View Details
                                        </Button>
                                      </div>
                                    ))}

                                    {categoryOrgs.length > 3 && (
                                      <div className="text-center">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="text-xs bg-background/80 backdrop-blur-md hover:bg-background/90 border border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-lg"
                                          onClick={() => {
                                            setSelectedCategory(category)
                                            setViewMode("grid")
                                          }}
                                        >
                                          Explore all {categoryOrgs.length} companies →
                                        </Button>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )
                            })}
                        </div>

                        <div className="text-center mt-8 pt-6 border-t border-border/50">
                          <p className="text-sm text-muted-foreground mb-2">
                            🗺️ Interactive Biotechnology Ecosystem • Click any region to explore
                          </p>
                          <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                              Live Data
                            </span>
                            <span className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                              {filteredOrganizations.length} Organizations
                            </span>
                            <span className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                              Real-time Updates
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {filteredOrganizations.map((org) => (
                      <Card key={org.id} className="hover:shadow-lg transition-shadow">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="min-w-0 flex-1">
                              <CardTitle className="text-base leading-tight">{org.name}</CardTitle>
                              <CardDescription className="text-xs">{org.category}</CardDescription>
                            </div>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  toggleBookmark(org.id)
                                }}
                                className="p-1 h-auto"
                              >
                                {isBookmarked(org.id) ? (
                                  <BookmarkCheck className="h-4 w-4 text-primary" />
                                ) : (
                                  <Bookmark className="h-4 w-4 text-muted-foreground hover:text-primary" />
                                )}
                              </Button>
                              <div className="flex items-center">
                                <Checkbox
                                  checked={compareList.includes(org.id)}
                                  onCheckedChange={() => toggleCompare(org.id)}
                                  disabled={!compareList.includes(org.id) && compareList.length >= 5}
                                  className="h-4 w-4 border-muted-foreground hover:border-primary data-[state=checked]:border-primary data-[state=checked]:bg-primary"
                                />
                              </div>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0 space-y-3">
                          <p className="text-xs text-muted-foreground line-clamp-2">{org.description}</p>

                          <div className="space-y-2">
                            <div className="flex items-center gap-1 text-xs">
                              <MapPin className="h-3 w-3 text-primary flex-shrink-0" />
                              <span className="truncate">{org.location}</span>
                            </div>
                            <div className="flex items-center gap-1 text-xs">
                              <Globe className="h-3 w-3 text-primary flex-shrink-0" />
                              <a
                                href={`https://${org.website}`}
                                className="text-primary hover:underline truncate"
                                target="_blank"
                                rel="noopener noreferrer"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {org.website}
                              </a>
                            </div>
                            <div className="flex items-center gap-1 text-xs">
                              <Phone className="h-3 w-3 text-primary flex-shrink-0" />
                              <a
                                href={`tel:${org.phone}`}
                                className="text-primary hover:underline"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {org.phone}
                              </a>
                            </div>
                            <div className="flex items-center gap-1 text-xs">
                              <Mail className="h-3 w-3 text-primary flex-shrink-0" />
                              <a
                                href={`mailto:${org.email}`}
                                className="text-primary hover:underline truncate"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {org.email}
                              </a>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-1">
                            {org.specialties.slice(0, 2).map((specialty) => (
                              <Badge key={specialty} variant="secondary" className="text-xs">
                                {specialty}
                              </Badge>
                            ))}
                            {org.specialties.length > 2 && (
                              <Badge variant="secondary" className="text-xs">
                                +{org.specialties.length - 2} more
                              </Badge>
                            )}
                          </div>

                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full mt-3 bg-transparent"
                            onClick={() => setSelectedOrg(org)}
                          >
                            View Details
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}

                {filteredOrganizations.length === 0 && (
                  <div className="text-center py-12">
                    <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-foreground mb-2">No organizations found</h3>
                    <p className="text-muted-foreground">Try adjusting your search criteria or filters</p>
                  </div>
                )}
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  )
}
