"use client"

import { useState, useMemo, useEffect } from "react"
import {
  Search,
  Filter,
  Building2,
  MapPin,
  Globe,
  Phone,
  Mail,
  Sparkles,
  GitCompare,
  X,
  Bookmark,
  BookmarkCheck,
  Grid3X3,
  Loader2,
  Clock,
  Calendar,
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ThemeToggle } from "@/components/theme-toggle"

type Organization = {
  id: number
  name: string
  category: string
  location: string
  employees: string
  website: string
  phone: string
  email: string
  description: string
  specialties: string[]
  founded: number
}

const categories = [
  "All Categories",
  "Research & Development",
  "Pharmaceuticals",
  "Medical Devices",
  "Diagnostics",
  "Investment & Funding",
  "Agricultural Biotechnology",
  "Marine Biotechnology",
  "Nanobiotechnology",
  "Bioinformatics & Data",
  "Synthetic Biology",
  "Regulatory & Compliance",
]

const employeeSizes = ["All Sizes", "10-50", "50-100", "100-500", "500-1000", "1000+"]

const isCompanyNameSearch = (query: string, organizations: Organization[]): boolean => {
  const companyNames = organizations.map((org) => org.name.toLowerCase())
  const queryLower = query.toLowerCase()

  return companyNames.some((name) => {
    // Check if query matches the full name or any word in the name
    if (name.includes(queryLower)) return true

    // Check if query matches any individual word in the company name
    const nameWords = name.split(/\s+/)
    return nameWords.some((word) => word.includes(queryLower) || queryLower.includes(word))
  })
}

const performIntelligentSearch = (query: string, organizations: Organization[]) => {
  if (!query.trim()) return organizations

  // Use regular search for company names
  if (isCompanyNameSearch(query, organizations)) {
    console.log("[v0] Using regular search for company name:", query)
    return organizations.filter((org) => org.name.toLowerCase().includes(query.toLowerCase()))
  }

  // Use AI-like search for complex queries
  console.log("[v0] Using intelligent search for complex query:", query)
  const queryLower = query.toLowerCase()

  // Enhanced semantic search with scoring
  const scoredResults = organizations.map((org) => {
    let score = 0

    // Direct matches get highest score
    if (org.name.toLowerCase().includes(queryLower)) score += 10
    if (org.description.toLowerCase().includes(queryLower)) score += 8
    if (org.category.toLowerCase().includes(queryLower)) score += 6

    // Specialty matches
    org.specialties.forEach((specialty) => {
      if (specialty.toLowerCase().includes(queryLower)) score += 5
    })

    // Location matches
    if (org.location.toLowerCase().includes(queryLower)) score += 4

    // Semantic keyword matching
    const keywords = {
      gene: ["gene therapy", "genetic", "genomic"],
      drug: ["pharmaceuticals", "drug discovery", "medicine"],
      ai: ["bioinformatics", "data", "analysis"],
      nano: ["nanobiotechnology", "nanoparticle"],
      marine: ["marine biotechnology", "ocean"],
      agriculture: ["agricultural biotechnology", "crop"],
      investment: ["venture capital", "funding"],
      regulatory: ["fda", "compliance", "approval"],
    }

    Object.entries(keywords).forEach(([key, synonyms]) => {
      if (queryLower.includes(key)) {
        synonyms.forEach((synonym) => {
          if (
            org.description.toLowerCase().includes(synonym) ||
            org.specialties.some((s) => s.toLowerCase().includes(synonym))
          ) {
            score += 3
          }
        })
      }
    })

    return { ...org, searchScore: score }
  })

  return scoredResults.filter((org) => org.searchScore > 0).sort((a, b) => b.searchScore - a.searchScore)
}

const parseCSV = (csvText: string): Organization[] => {
  console.log("[v0] Starting CSV parsing...")
  const lines = csvText.trim().split("\n")
  console.log("[v0] CSV lines count:", lines.length)

  if (lines.length < 2) {
    console.log("[v0] CSV has insufficient data - only", lines.length, "lines")
    return []
  }

  // Parse CSV with proper handling of quoted fields
  const parseCSVLine = (line: string): string[] => {
    const result: string[] = []
    let current = ""
    let inQuotes = false

    for (let i = 0; i < line.length; i++) {
      const char = line[i]

      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ""
      } else {
        current += char
      }
    }
    result.push(current.trim())
    return result
  }

  const headers = parseCSVLine(lines[0])
  console.log("[v0] CSV headers:", headers)

  const organizations = lines
    .slice(1)
    .map((line, index) => {
      const values = parseCSVLine(line)
      const org: any = {}

      headers.forEach((header, i) => {
        org[header] = values[i] || ""
      })

      // Map the specific CSV structure to our organization format
      const transformedOrg = {
        id: index + 1,
        name: org["Research area/lab/scientist"] || org.name || "",
        category: org["Organization"] || "Industrial Biotechnology",
        location: org["Location"] || "",
        employees: "50-100", // Default since not in CSV
        website: org["Weblink"] || "",
        phone: org["Contact Person"] || "",
        email: org["Email"] || "",
        description: `${org["Research area/lab/scientist"] || "Biotechnology organization"} - ${org["Organization"] || "Industrial Biotechnology"}`,
        specialties: org["Organization"] ? [org["Organization"]] : ["Biotechnology"],
        founded: 2020, // Default since not in CSV
      }

      if (index < 3) {
        console.log(`[v0] Transformed organization ${index + 1}:`, transformedOrg)
      }

      return transformedOrg
    })
    .filter((org) => org.name && org.name.trim() !== "") // Filter out entries with empty names

  console.log("[v0] Final organizations count after filtering:", organizations.length)
  return organizations
}

const useBookmarks = () => {
  const [bookmarks, setBookmarks] = useState<number[]>([])

  useEffect(() => {
    const saved = localStorage.getItem("biotech-bookmarks")
    if (saved) {
      setBookmarks(JSON.parse(saved))
    }
  }, [])

  const toggleBookmark = (orgId: number) => {
    const newBookmarks = bookmarks.includes(orgId) ? bookmarks.filter((id) => id !== orgId) : [...bookmarks, orgId]

    setBookmarks(newBookmarks)
    localStorage.setItem("biotech-bookmarks", JSON.stringify(newBookmarks))
  }

  const isBookmarked = (orgId: number) => bookmarks.includes(orgId)

  return { bookmarks, toggleBookmark, isBookmarked }
}

export default function BiotechDirectory() {
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [selectedEmployeeSize, setSelectedEmployeeSize] = useState("All Sizes")
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null)
  const [isAiSearch, setIsAiSearch] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [compareList, setCompareList] = useState<number[]>([])
  const [showComparison, setShowComparison] = useState(false)
  const [showBookmarksOnly, setShowBookmarksOnly] = useState(false)
  const [viewMode, setViewMode] = useState<"grid" | "timeline">("grid")
  const { bookmarks, toggleBookmark, isBookmarked } = useBookmarks()

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        setLoading(true)
        setError(null)

        console.log("[v0] Fetching data from GitHub Gist...")
        const response = await fetch(
          "https://gist.githubusercontent.com/RushiChaganti/54e669b7eee9f6bca2637458d0308f01/raw/",
        )

        if (!response.ok) {
          throw new Error(`Failed to fetch data: ${response.status}`)
        }

        const csvText = await response.text()
        console.log("[v0] Raw CSV data received:", csvText.substring(0, 500) + "...")
        console.log("[v0] CSV data length:", csvText.length)

        const parsedOrganizations = parseCSV(csvText)
        console.log("[v0] Parsed organizations count:", parsedOrganizations.length)
        console.log("[v0] First parsed organization:", parsedOrganizations[0])

        setOrganizations(parsedOrganizations)
      } catch (err) {
        console.error("[v0] Error fetching organizations:", err)
        setError(err instanceof Error ? err.message : "Failed to load organizations")
      } finally {
        setLoading(false)
      }
    }

    fetchOrganizations()
  }, [])

  const filteredOrganizations = useMemo(() => {
    let results = organizations

    if (showBookmarksOnly) {
      results = results.filter((org) => bookmarks.includes(org.id))
    }

    if (searchTerm.trim()) {
      const isCompanySearch = isCompanyNameSearch(searchTerm, organizations)
      setIsAiSearch(!isCompanySearch)
      results = performIntelligentSearch(searchTerm, results)
    }

    if (selectedCategory !== "All Categories") {
      results = results.filter((org) => org.category === selectedCategory)
    }

    if (selectedEmployeeSize !== "All Sizes") {
      results = results.filter((org) => org.employees === selectedEmployeeSize)
    }

    return results
  }, [searchTerm, selectedCategory, selectedEmployeeSize, showBookmarksOnly, bookmarks, organizations])

  const toggleCompare = (orgId: number) => {
    if (compareList.includes(orgId)) {
      setCompareList(compareList.filter((id) => id !== orgId))
    } else if (compareList.length < 5) {
      setCompareList([...compareList, orgId])
    }
  }

  const clearComparison = () => {
    setCompareList([])
    setShowComparison(false)
  }

  const compareOrganizations = organizations.filter((org) => compareList.includes(org.id))

  // Group organizations by decade for timeline view
  const timelineData = useMemo(() => {
    const grouped = filteredOrganizations.reduce((acc, org) => {
      const decade = Math.floor(org.founded / 10) * 10
      if (!acc[decade]) {
        acc[decade] = []
      }
      acc[decade].push(org)
      return acc
    }, {} as Record<number, Organization[]>)

    return Object.entries(grouped)
      .map(([decade, orgs]) => ({
        decade: parseInt(decade),
        organizations: orgs.sort((a, b) => a.founded - b.founded),
        count: orgs.length
      }))
      .sort((a, b) => b.decade - a.decade) // Most recent first
  }, [filteredOrganizations])

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">Loading Organizations</h2>
          <p className="text-muted-foreground">Fetching biotechnology companies data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Building2 className="h-8 w-8 text-destructive mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">Error Loading Data</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <button
                onClick={() => {
                  setSelectedOrg(null)
                  setShowComparison(false)
                  setSearchTerm("")
                  setSelectedCategory("All Categories")
                  setSelectedEmployeeSize("All Sizes")
                  setShowBookmarksOnly(false)
                }}
                className="flex items-center gap-3 hover:opacity-80 transition-opacity"
              >
                <Building2 className="h-8 w-8 text-primary" />
                <h1 className="text-3xl font-bold text-foreground">Biogrofe</h1>
              </button>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowBookmarksOnly(!showBookmarksOnly)}
                className={`flex items-center gap-2 ${showBookmarksOnly ? "bg-primary text-primary-foreground" : ""}`}
              >
                <Bookmark className="h-4 w-4" />
                <span className="hidden sm:inline">Bookmarks ({bookmarks.length})</span>
              </Button>

              {compareList.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowComparison(!showComparison)}
                  className="flex items-center gap-2 bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800"
                >
                  <GitCompare className="h-4 w-4" />
                  <span className="hidden sm:inline">Compare ({compareList.length})</span>
                </Button>
              )}

              <div className="flex items-center border border-border rounded-md">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none border-r"
                >
                  <Grid3X3 className="h-4 w-4" />
                  <span className="hidden sm:inline ml-1">Grid</span>
                </Button>
                <Button
                  variant={viewMode === "timeline" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("timeline")}
                  className="rounded-l-none"
                >
                  <Clock className="h-4 w-4" />
                  <span className="hidden sm:inline ml-1">Timeline</span>
                </Button>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                <span className="hidden sm:inline">Filters</span>
              </Button>
              <ThemeToggle />
            </div>
          </div>

          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              {isAiSearch && (
                <Sparkles className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-primary animate-pulse" />
              )}
              <Input
                placeholder="Search organizations, specialties, or ask complex questions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 pr-12 h-12 text-lg"
              />
            </div>
            {searchTerm && (
              <p className="text-sm text-muted-foreground mt-2 text-center">
                {isAiSearch ? (
                  <span className="flex items-center justify-center gap-1">
                    <Sparkles className="h-4 w-4 text-primary" />
                    Using AI-powered search
                  </span>
                ) : (
                  "Using direct company search"
                )}
              </p>
            )}
          </div>

          <p className="text-muted-foreground text-lg text-center mt-4">
            Discover leading biotechnology companies, research institutions, and industry partners
          </p>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {showFilters && (
            <>
              <div className="fixed inset-0 bg-black/50 z-40 lg:hidden" onClick={() => setShowFilters(false)} />
              <aside className="fixed top-0 right-0 h-full w-80 bg-background border-l border-border z-50 lg:relative lg:w-64 lg:h-auto lg:bg-transparent lg:border-l-0 lg:z-auto">
                <div className="p-4 lg:p-0">
                  <Card className="lg:sticky lg:top-4">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between text-base">
                        <span className="flex items-center gap-2">
                          <Filter className="h-4 w-4" />
                          Filters
                        </span>
                        <Button variant="ghost" size="sm" onClick={() => setShowFilters(false)}>
                          ×
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3 pt-0">
                      <div>
                        <label className="text-xs font-medium text-foreground mb-1 block">Category</label>
                        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-xs font-medium text-foreground mb-1 block">Company Size</label>
                        <Select value={selectedEmployeeSize} onValueChange={setSelectedEmployeeSize}>
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {employeeSizes.map((size) => (
                              <SelectItem key={size} value={size}>
                                {size}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="pt-2 border-t border-border">
                        <p className="text-xs text-muted-foreground">
                          {filteredOrganizations.length} organization{filteredOrganizations.length !== 1 ? "s" : ""}{" "}
                          found
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </aside>
            </>
          )}

          <main className="flex-1">
            {showComparison && compareOrganizations.length > 0 ? (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-semibold text-foreground">
                    Compare Organizations ({compareOrganizations.length})
                  </h2>
                  <Button variant="outline" onClick={() => setShowComparison(false)}>
                    Back to Results
                  </Button>
                </div>

                <div className="overflow-x-auto">
                  <div className="flex gap-4 min-w-max">
                    {compareOrganizations.map((org) => (
                      <Card key={org.id} className="w-80 flex-shrink-0">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="min-w-0 flex-1">
                              <CardTitle className="text-lg">{org.name}</CardTitle>
                              <CardDescription>{org.category}</CardDescription>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleCompare(org.id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <p className="text-sm text-muted-foreground">{org.description}</p>

                          <div className="space-y-3">
                            <div className="flex items-center gap-2 text-sm">
                              <MapPin className="h-4 w-4 text-primary flex-shrink-0" />
                              <span>{org.location}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Globe className="h-4 w-4 text-primary flex-shrink-0" />
                              <a
                                href={`https://${org.website}`}
                                className="text-primary hover:underline truncate"
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                {org.website}
                              </a>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Phone className="h-4 w-4 text-primary flex-shrink-0" />
                              <a href={`tel:${org.phone}`} className="text-primary hover:underline">
                                {org.phone}
                              </a>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Mail className="h-4 w-4 text-primary flex-shrink-0" />
                              <a href={`mailto:${org.email}`} className="text-primary hover:underline truncate">
                                {org.email}
                              </a>
                            </div>
                          </div>

                          <div>
                            <h4 className="font-medium text-sm mb-2">Specialties</h4>
                            <div className="flex flex-wrap gap-1">
                              {org.specialties.map((specialty) => (
                                <Badge key={specialty} variant="secondary" className="text-xs">
                                  {specialty}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                <div className="flex justify-center">
                  <Button variant="outline" onClick={clearComparison}>
                    Clear Comparison
                  </Button>
                </div>
              </div>
            ) : selectedOrg ? (
              <Card>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-2xl">{selectedOrg.name}</CardTitle>
                      <CardDescription className="text-lg mt-1">{selectedOrg.category}</CardDescription>
                    </div>
                    <Button variant="outline" onClick={() => setSelectedOrg(null)}>
                      Back to Results
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <p className="text-foreground text-lg leading-relaxed">{selectedOrg.description}</p>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <MapPin className="h-5 w-5 text-primary" />
                        <span>{selectedOrg.location}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Globe className="h-5 w-5 text-primary" />
                        <a href={`https://${selectedOrg.website}`} className="text-primary hover:underline">
                          {selectedOrg.website}
                        </a>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Phone className="h-5 w-5 text-primary" />
                        <span>{selectedOrg.phone}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Mail className="h-5 w-5 text-primary" />
                        <a href={`mailto:${selectedOrg.email}`} className="text-primary hover:underline truncate">
                          {selectedOrg.email}
                        </a>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-foreground mb-3">Specialties</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedOrg.specialties.map((specialty) => (
                        <Badge key={specialty} variant="secondary">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-semibold text-foreground">
                    {showBookmarksOnly ? "Bookmarked " : ""}Organizations ({filteredOrganizations.length})
                  </h2>
                  {compareList.length > 0 && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {compareList.length}/5 selected for comparison
                      </span>
                      <Button variant="outline" size="sm" onClick={clearComparison}>
                        Clear
                      </Button>
                    </div>
                  )}
                </div>

                {viewMode === "timeline" ? (
                  <div className="space-y-6">
                    <div className="relative bg-gradient-to-br from-slate-50 via-blue-50 to-green-50 dark:from-slate-950 dark:via-blue-950 dark:to-green-950 border border-border rounded-2xl p-8 overflow-hidden">
                      {/* Background Pattern */}
                      <div className="absolute inset-0 opacity-10">
                        <div
                          className="absolute inset-0"
                          style={{
                            backgroundImage: `linear-gradient(90deg, #3b82f6 1px, transparent 1px),
                                           linear-gradient(0deg, #10b981 1px, transparent 1px)`,
                            backgroundSize: "40px 40px",
                          }}
                        />
                      </div>

                      {/* Floating Timeline Elements */}
                      <div className="absolute inset-0 overflow-hidden pointer-events-none">
                        {[...Array(15)].map((_, i) => (
                          <div
                            key={i}
                            className="absolute w-1 h-8 bg-primary/20 rounded-full animate-pulse"
                            style={{
                              left: `${Math.random() * 100}%`,
                              top: `${Math.random() * 100}%`,
                              animationDelay: `${Math.random() * 3}s`,
                              animationDuration: `${2 + Math.random() * 2}s`,
                              transform: `rotate(${Math.random() * 360}deg)`,
                            }}
                          />
                        ))}
                      </div>

                      <div className="relative z-10">
                        <div className="text-center mb-8">
                          <div className="relative inline-block">
                            <Clock className="h-16 w-16 text-primary mx-auto mb-4 drop-shadow-lg" />
                            <div className="absolute -inset-2 bg-primary/20 rounded-full blur-xl animate-pulse" />
                          </div>
                          <h3 className="text-3xl font-bold text-foreground mb-3 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                            Biotechnology Evolution Timeline
                          </h3>
                          <p className="text-muted-foreground text-lg">
                            Explore the industry's growth through decades of innovation
                          </p>
                        </div>

                        {/* Timeline Content */}
                        <div className="space-y-8">
                          {timelineData.map((period) => (
                            <div key={period.decade} className="relative">
                              {/* Timeline Line */}
                              <div className="absolute left-8 top-16 bottom-0 w-0.5 bg-gradient-to-b from-primary to-blue-500 opacity-30" />

                              {/* Decade Header */}
                              <div className="flex items-center gap-4 mb-6">
                                <div className="relative">
                                  <div className="w-16 h-16 bg-gradient-to-br from-primary to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                                    <Calendar className="h-8 w-8 text-white" />
                                  </div>
                                  <div className="absolute -inset-1 bg-primary/20 rounded-full blur-md animate-pulse" />
                                </div>
                                <div>
                                  <h3 className="text-2xl font-bold text-foreground">
                                    {period.decade}s Era
                                  </h3>
                                  <p className="text-muted-foreground">
                                    {period.count} organization{period.count !== 1 ? 's' : ''} founded
                                  </p>
                                </div>
                                <div className="flex-1 h-px bg-gradient-to-r from-primary/50 to-transparent" />
                              </div>

                              {/* Organizations Grid */}
                              <div className="ml-20 grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                                {period.organizations.map((org, orgIndex) => (
                                  <Card
                                    key={org.id}
                                    className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-l-4 border-l-primary/50"
                                    style={{
                                      animationDelay: `${orgIndex * 0.1}s`,
                                    }}
                                  >
                                    <CardHeader className="pb-3">
                                      <div className="flex items-start justify-between">
                                        <div className="min-w-0 flex-1">
                                          <div className="flex items-center gap-2 mb-1">
                                            <CardTitle className="text-base leading-tight">{org.name}</CardTitle>
                                            <Badge variant="outline" className="text-xs bg-primary/10 text-primary border-primary/30">
                                              {org.founded}
                                            </Badge>
                                          </div>
                                          <CardDescription className="text-xs">{org.category}</CardDescription>
                                        </div>
                                        <div className="flex items-center gap-1">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={(e) => {
                                              e.stopPropagation()
                                              toggleBookmark(org.id)
                                            }}
                                            className="p-1 h-auto"
                                          >
                                            {isBookmarked(org.id) ? (
                                              <BookmarkCheck className="h-4 w-4 text-primary" />
                                            ) : (
                                              <Bookmark className="h-4 w-4 text-muted-foreground hover:text-primary" />
                                            )}
                                          </Button>
                                          <Checkbox
                                            checked={compareList.includes(org.id)}
                                            onCheckedChange={() => toggleCompare(org.id)}
                                            disabled={!compareList.includes(org.id) && compareList.length >= 5}
                                            className="h-4 w-4 border-muted-foreground hover:border-primary data-[state=checked]:border-primary data-[state=checked]:bg-primary"
                                          />
                                        </div>
                                      </div>
                                    </CardHeader>
                                    <CardContent className="pt-0 space-y-3">
                                      <p className="text-xs text-muted-foreground line-clamp-2">{org.description}</p>

                                      <div className="space-y-2">
                                        <div className="flex items-center gap-1 text-xs">
                                          <MapPin className="h-3 w-3 text-primary flex-shrink-0" />
                                          <span className="truncate">{org.location}</span>
                                        </div>
                                        <div className="flex items-center gap-1 text-xs">
                                          <Globe className="h-3 w-3 text-primary flex-shrink-0" />
                                          <a
                                            href={`https://${org.website}`}
                                            className="text-primary hover:underline truncate"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            onClick={(e) => e.stopPropagation()}
                                          >
                                            {org.website}
                                          </a>
                                        </div>
                                      </div>

                                      <div className="flex flex-wrap gap-1">
                                        {org.specialties.slice(0, 2).map((specialty) => (
                                          <Badge key={specialty} variant="secondary" className="text-xs">
                                            {specialty}
                                          </Badge>
                                        ))}
                                        {org.specialties.length > 2 && (
                                          <Badge variant="secondary" className="text-xs">
                                            +{org.specialties.length - 2} more
                                          </Badge>
                                        )}
                                      </div>

                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="w-full mt-3 bg-transparent"
                                        onClick={() => setSelectedOrg(org)}
                                      >
                                        View Details
                                      </Button>
                                    </CardContent>
                                  </Card>
                                ))}
                              </div>
                            </div>
                          ))}

                        </div>

                        {/* Timeline Footer */}
                        <div className="text-center mt-8 pt-6 border-t border-border/50">
                          <p className="text-sm text-muted-foreground mb-2">
                            📅 Biotechnology Evolution Timeline • Explore decades of innovation
                          </p>
                          <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                              Historical Data
                            </span>
                            <span className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                              {filteredOrganizations.length} Organizations
                            </span>
                            <span className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                              {timelineData.length} Time Periods
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {filteredOrganizations.map((org) => (
                      <Card key={org.id} className="hover:shadow-lg transition-shadow">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="min-w-0 flex-1">
                              <CardTitle className="text-base leading-tight">{org.name}</CardTitle>
                              <CardDescription className="text-xs">{org.category}</CardDescription>
                            </div>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  toggleBookmark(org.id)
                                }}
                                className="p-1 h-auto"
                              >
                                {isBookmarked(org.id) ? (
                                  <BookmarkCheck className="h-4 w-4 text-primary" />
                                ) : (
                                  <Bookmark className="h-4 w-4 text-muted-foreground hover:text-primary" />
                                )}
                              </Button>
                              <div className="flex items-center">
                                <Checkbox
                                  checked={compareList.includes(org.id)}
                                  onCheckedChange={() => toggleCompare(org.id)}
                                  disabled={!compareList.includes(org.id) && compareList.length >= 5}
                                  className="h-4 w-4 border-muted-foreground hover:border-primary data-[state=checked]:border-primary data-[state=checked]:bg-primary"
                                />
                              </div>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0 space-y-3">
                          <p className="text-xs text-muted-foreground line-clamp-2">{org.description}</p>

                          <div className="space-y-2">
                            <div className="flex items-center gap-1 text-xs">
                              <MapPin className="h-3 w-3 text-primary flex-shrink-0" />
                              <span className="truncate">{org.location}</span>
                            </div>
                            <div className="flex items-center gap-1 text-xs">
                              <Globe className="h-3 w-3 text-primary flex-shrink-0" />
                              <a
                                href={`https://${org.website}`}
                                className="text-primary hover:underline truncate"
                                target="_blank"
                                rel="noopener noreferrer"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {org.website}
                              </a>
                            </div>
                            <div className="flex items-center gap-1 text-xs">
                              <Phone className="h-3 w-3 text-primary flex-shrink-0" />
                              <a
                                href={`tel:${org.phone}`}
                                className="text-primary hover:underline"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {org.phone}
                              </a>
                            </div>
                            <div className="flex items-center gap-1 text-xs">
                              <Mail className="h-3 w-3 text-primary flex-shrink-0" />
                              <a
                                href={`mailto:${org.email}`}
                                className="text-primary hover:underline truncate"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {org.email}
                              </a>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-1">
                            {org.specialties.slice(0, 2).map((specialty) => (
                              <Badge key={specialty} variant="secondary" className="text-xs">
                                {specialty}
                              </Badge>
                            ))}
                            {org.specialties.length > 2 && (
                              <Badge variant="secondary" className="text-xs">
                                +{org.specialties.length - 2} more
                              </Badge>
                            )}
                          </div>

                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full mt-3 bg-transparent"
                            onClick={() => setSelectedOrg(org)}
                          >
                            View Details
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}

                {filteredOrganizations.length === 0 && (
                  <div className="text-center py-12">
                    <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-foreground mb-2">No organizations found</h3>
                    <p className="text-muted-foreground">Try adjusting your search criteria or filters</p>
                  </div>
                )}
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  )
}
